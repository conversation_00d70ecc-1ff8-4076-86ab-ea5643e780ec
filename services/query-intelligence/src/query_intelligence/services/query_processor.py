import hashlib
import json
import re
import time
from datetime import timedelta
from typing import List, Dict, Optional, Any

import structlog
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from query_intelligence.models import (
    QueryRequest,
    QueryResult,
    QueryIntent,
    CodeReference,
    QueryContext,
    IntentAnalysis,
    CodeChunk,
    GeneratedResponse,
)
from query_intelligence.clients.redis import get_redis_client
from query_intelligence.clients.pattern_mining import get_pattern_mining_client
from query_intelligence.config.settings import get_settings
from query_intelligence.services.semantic_search import SemanticSearchService
from query_intelligence.services.llm_service_v2 import LLMServiceV2 as LLMService
from query_intelligence.services.fallback_handler import FallbackHandler
from query_intelligence.services.cache_manager import get_cache_manager
from query_intelligence.services.language_detector import get_language_support
from query_intelligence.utils.circuit_breaker import CircuitBreakerError

logger = structlog.get_logger()
settings = get_settings()


class QueryProcessor:
    """Main query processing engine for natural language code queries"""

    def __init__(self):
        self.semantic_search = SemanticSearchService()
        self.llm_service = LLMService()
        self.redis_client = get_redis_client()
        self.pattern_mining_client = get_pattern_mining_client()
        self.cache_manager = get_cache_manager()
        self.language_support = get_language_support()
        self._cache_ttl = timedelta(hours=settings.CACHE_TTL_HOURS)

    def _create_context(self, request: QueryRequest) -> QueryContext:
        """Create query context from request"""
        return QueryContext(
            repository_id=request.repository_id,
            user_id=request.user_id or "anonymous",
            session_id=request.session_id,
            history=request.context_history or [],
            filters=request.filters or {},
        )

    async def process_query(self, request: QueryRequest) -> QueryResult:
        """Process a natural language query about code"""
        start_time = time.time()

        # Create context from request
        context = self._create_context(request)

        logger.info(
            "processing_query",
            query=request.query,
            repository_id=context.repository_id,
            user_id=context.user_id,
            session_id=context.session_id,
        )

        try:
            # Check cache first (optimized for <100ms)
            cache_response = await self.cache_manager.get_cached_query_result(
                request.query,
                request.repository_id,
                request.filters
            )
            cached_result, hit_level = cache_response if isinstance(cache_response, tuple) else (cache_response, None)
            if cached_result:
                logger.info("query_cache_hit")
                # Update processing time even for cached results
                try:
                    cached_result.execution_time_ms = (time.time() - start_time) * 1000
                except Exception:
                    pass
                return cached_result

            # Step 1: Detect language and translate if needed
            lang_result = await self.language_support.detect_language(request.query)
            
            # Store original query for response
            original_query = request.query
            
            # Translate if needed
            if lang_result.needs_translation:
                translation = await self.language_support.translate_query(
                    request.query,
                    lang_result.detected_language,
                    preserve_code_terms=True
                )
                request.query = translation.translated_query
                logger.info(
                    "query_translated",
                    from_lang=lang_result.detected_language,
                    to_lang="en"
                )
            
            # Step 2: Analyze query intent
            intent_analysis = await self._analyze_intent(request.query, context)

            # Step 2: Generate query embedding
            query_embedding = await self.semantic_search.generate_embedding(
                request.query, context_type="query"
            )

            # Step 3: Perform semantic search
            search_results = await self.semantic_search.search(
                embedding=query_embedding,
                repository_id=context.repository_id,
                filters=self._build_search_filters(intent_analysis, context),
                limit=20,
            )

            # Step 4: Rerank results based on intent
            ranked_chunks = await self._rerank_chunks(
                request.query, intent_analysis, search_results.chunks
            )

            # Step 4.5: Detect patterns if applicable
            pattern_insights = None
            if self._should_analyze_patterns(intent_analysis):
                pattern_insights = await self._analyze_patterns(
                    ranked_chunks[:10], intent_analysis, context
                )

            # Step 5: Generate response
            try:
                generated_response = await self.llm_service.generate_response(
                    query=request.query,
                    intent=intent_analysis,
                    code_chunks=ranked_chunks[:10],
                    context=context,
                    pattern_insights=pattern_insights,
                )
            except CircuitBreakerError as e:
                # Use fallback handler
                generated_response = await FallbackHandler.handle_llm_fallback(
                    query=request.query,
                    intent=intent_analysis,
                    code_chunks=ranked_chunks[:10],
                    context=context,
                    error=e,
                )

            # Step 6: Extract code references
            references = self._extract_references(ranked_chunks[:5])

            # Step 7: Generate follow-up questions
            follow_ups = await self._generate_follow_ups(
                request.query, generated_response, context
            )

            # Calculate execution time
            execution_time_ms = (time.time() - start_time) * 1000

            # Create result
            result = QueryResult(
                answer=generated_response.text,
                intent=intent_analysis.primary_intent,
                confidence=generated_response.confidence,
                references=references,
                execution_time_ms=execution_time_ms,
                follow_up_questions=follow_ups,
                metadata={
                    "model_used": generated_response.model_used,
                    "chunks_retrieved": len(search_results.chunks),
                    "chunks_used": len(ranked_chunks[:10]),
                    "intent_confidence": intent_analysis.confidence,
                    "pattern_insights": pattern_insights,
                    "query_language": lang_result.detected_language,
                    "was_translated": lang_result.needs_translation,
                    "original_query": original_query if lang_result.needs_translation else None,
                },
            )

            # Cache the result for future queries
            await self.cache_manager.cache_query_result(
                request.query,
                request.repository_id,
                result,
                request.filters
            )

            # Track metrics
            await self._track_metrics(result, execution_time_ms)

            logger.info(
                "query_processed",
                query_id=context.session_id,
                execution_time_ms=execution_time_ms,
                confidence=result.confidence,
                reference_count=len(references),
                intent=intent_analysis.primary_intent.value,
            )

            return result

        except Exception as e:
            logger.error(
                "query_processing_failed",
                query=request.query,
                error=str(e),
                exc_info=True,
            )
            raise

    async def _analyze_intent(
        self, query: str, context: QueryContext
    ) -> IntentAnalysis:
        """Analyze the intent of the query using LLM"""
        prompt = f"""
        Analyze the following code-related query and provide:
        1. Primary intent (one of: explain, find, debug, refactor, analyze, compare)
        2. Code elements mentioned (functions, classes, patterns, etc.)
        3. Scope (file, module, or repository)
        4. Required context depth (shallow, normal, or deep)
        
        Query: {query}
        Repository: {context.repository_id}
        
        Respond in JSON format:
        {{
            "primary_intent": "explain|find|debug|refactor|analyze|compare",
            "code_elements": ["list", "of", "elements"],
            "scope": "file|module|repository",
            "context_depth": "shallow|normal|deep",
            "confidence": 0.0-1.0
        }}
        """

        try:
            response = await self.llm_service.generate_json_response(prompt)

            # Parse response and create IntentAnalysis
            intent_map = {
                "explain": QueryIntent.EXPLAIN,
                "find": QueryIntent.FIND,
                "debug": QueryIntent.DEBUG,
                "refactor": QueryIntent.REFACTOR,
                "analyze": QueryIntent.ANALYZE,
                "compare": QueryIntent.COMPARE,
            }

            return IntentAnalysis(
                primary_intent=intent_map.get(
                    response.get("primary_intent", "unknown"), QueryIntent.UNKNOWN
                ),
                code_elements=response.get("code_elements", []),
                scope=response.get("scope", "repository"),
                context_depth=response.get("context_depth", "normal"),
                confidence=response.get("confidence", 0.7),
            )
        except CircuitBreakerError:
            # Fallback: Simple keyword-based intent detection
            logger.warning("intent_analysis_fallback")
            return self._fallback_intent_analysis(query)

    async def _rerank_chunks(
        self, query: str, intent: IntentAnalysis, chunks: List[CodeChunk]
    ) -> List[CodeChunk]:
        """Rerank code chunks based on intent and relevance"""
        if not chunks:
            return chunks

        # Extract features for each chunk
        features = []
        for chunk in chunks:
            feature_vector = self._extract_chunk_features(chunk, intent)
            features.append(feature_vector)

        # Calculate intent-based relevance scores
        query_features = self._extract_query_features(query, intent)
        if features:
            scores = cosine_similarity([query_features], features)[0]
        else:
            scores = np.zeros(len(chunks))

        # Combine scores
        for i, chunk in enumerate(chunks):
            chunk.combined_score = (
                0.6 * chunk.similarity_score  # Semantic similarity
                + 0.3 * scores[i]  # Intent-based score
                + 0.1 * chunk.recency_score  # Recency boost
            )

        # Sort by combined score
        return sorted(chunks, key=lambda x: x.combined_score, reverse=True)

    def _extract_chunk_features(
        self, chunk: CodeChunk, intent: IntentAnalysis
    ) -> np.ndarray:
        """Extract feature vector from code chunk for reranking"""
        features = []

        # Intent-specific features
        if intent.primary_intent == QueryIntent.DEBUG:
            features.extend(
                [
                    float("error" in chunk.content.lower()),
                    float("exception" in chunk.content.lower()),
                    float(
                        "catch" in chunk.content.lower()
                        or "except" in chunk.content.lower()
                    ),
                ]
            )
        elif intent.primary_intent == QueryIntent.EXPLAIN:
            features.extend(
                [
                    float("class" in chunk.content.lower()),
                    float(
                        "function" in chunk.content.lower()
                        or "def" in chunk.content.lower()
                    ),
                    float(
                        any(
                            elem.lower() in chunk.content.lower()
                            for elem in intent.code_elements
                        )
                    ),
                ]
            )
        else:
            features.extend([0.0, 0.0, 0.0])

        # General features
        features.extend(
            [
                chunk.similarity_score,
                len(chunk.content.split("\n")),  # Number of lines
                float(
                    chunk.language in ["python", "javascript", "typescript"]
                ),  # Popular languages
            ]
        )

        return np.array(features)

    def _extract_query_features(self, query: str, intent: IntentAnalysis) -> np.ndarray:
        """Extract feature vector from query for reranking"""
        features = []

        # Intent-specific query features
        if intent.primary_intent == QueryIntent.DEBUG:
            features.extend(
                [
                    float("error" in query.lower()),
                    float("bug" in query.lower() or "issue" in query.lower()),
                    float("fix" in query.lower()),
                ]
            )
        elif intent.primary_intent == QueryIntent.EXPLAIN:
            features.extend(
                [
                    float("how" in query.lower() or "what" in query.lower()),
                    float("work" in query.lower() or "does" in query.lower()),
                    float("explain" in query.lower() or "understand" in query.lower()),
                ]
            )
        else:
            features.extend([0.0, 0.0, 0.0])

        # General query features
        features.extend(
            [
                intent.confidence,
                len(query.split()),  # Query length
                float(intent.context_depth == "deep"),
            ]
        )

        return np.array(features)

    def _extract_references(self, chunks: List[CodeChunk]) -> List[CodeReference]:
        """Extract code references from top chunks"""
        references = []

        for chunk in chunks:
            ref = CodeReference(
                file_path=chunk.file_path,
                start_line=chunk.start_line,
                end_line=chunk.end_line,
                snippet=self._format_snippet(chunk.content),
                relevance_score=chunk.combined_score,
                language=chunk.language,
            )
            references.append(ref)

        return references

    def _format_snippet(self, content: str, max_lines: int = 10) -> str:
        """Format code snippet for display"""
        lines = content.split("\n")
        if len(lines) > max_lines:
            # Take first and last parts
            head = lines[: max_lines // 2]
            tail = lines[-(max_lines // 2) :]
            return "\n".join(head) + "\n...\n" + "\n".join(tail)
        return content

    async def _generate_follow_ups(
        self, query: str, response: GeneratedResponse, context: QueryContext
    ) -> List[str]:
        """Generate relevant follow-up questions"""
        prompt = f"""
        Based on this Q&A about code:
        Question: {query}
        Answer: {response.text[:500]}...
        
        Generate 3 relevant follow-up questions that would help the user
        dive deeper into the topic or explore related areas.
        
        Return as a JSON array of strings.
        """

        follow_ups = await self.llm_service.generate_json_response(prompt)
        if isinstance(follow_ups, list):
            return follow_ups[:3]
        return []

    def _build_search_filters(
        self, intent: IntentAnalysis, context: QueryContext
    ) -> Dict[str, Any]:
        """Build search filters based on intent and context"""
        filters = context.filters.copy()

        # Add intent-based filters
        if intent.scope == "file" and intent.code_elements:
            filters["file_pattern"] = intent.code_elements[0]

        if intent.primary_intent == QueryIntent.DEBUG:
            filters["prefer_error_handling"] = True

        return filters

    def _generate_cache_key(self, request: QueryRequest) -> str:
        """Generate cache key for query"""
        key_data = {
            "query": request.query.lower().strip(),
            "repository_id": request.repository_id,
            "filters": request.filters or {},
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return f"query:v1:{hashlib.md5(key_str.encode()).hexdigest()}"

    async def _get_cached_result(self, cache_key: str) -> Optional[QueryResult]:
        """Get cached query result"""
        try:
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                return QueryResult(**data)
        except Exception as e:
            logger.warning("cache_read_failed", error=str(e))
        return None

    async def _cache_result(self, cache_key: str, result: QueryResult):
        """Cache query result"""
        try:
            data = result.model_dump(mode="json")
            await self.redis_client.set(
                cache_key, json.dumps(data), ex=int(self._cache_ttl.total_seconds())
            )
        except Exception as e:
            logger.warning("cache_write_failed", error=str(e))

    async def _track_metrics(self, result: QueryResult, execution_time_ms: float):
        """Track query processing metrics"""
        from query_intelligence.utils.metrics import MetricsCollector

        # Convert ms to seconds for Prometheus
        execution_time_seconds = execution_time_ms / 1000.0

        # Record query metrics
        MetricsCollector.record_query(
            intent=result.intent.value,
            status="success" if result.confidence >= 0.5 else "low_confidence",
        )

        # Record query duration
        MetricsCollector.record_query_duration(
            intent=result.intent.value, duration_seconds=execution_time_seconds
        )

        # Record response confidence
        MetricsCollector.record_response_confidence(
            intent=result.intent.value, confidence=result.confidence
        )

        # Log for debugging
        logger.info(
            "query_metrics",
            intent=result.intent.value,
            confidence=result.confidence,
            execution_time_ms=execution_time_ms,
            reference_count=len(result.references),
        )

    def _fallback_intent_analysis(self, query: str) -> IntentAnalysis:
        """Simple keyword-based intent detection for fallback"""
        query_lower = query.lower()

        # Keyword mapping
        intent_keywords = {
            QueryIntent.EXPLAIN: [
                "explain",
                "how does",
                "what is",
                "describe",
                "understand",
            ],
            QueryIntent.FIND: ["find", "locate", "where", "search", "show me"],
            QueryIntent.DEBUG: ["debug", "error", "bug", "issue", "problem", "fix"],
            QueryIntent.REFACTOR: ["refactor", "improve", "optimize", "clean up"],
            QueryIntent.ANALYZE: ["analyze", "review", "assess", "evaluate"],
            QueryIntent.COMPARE: ["compare", "difference", "versus", "vs", "better"],
        }

        # Score each intent
        scores = {}
        for intent, keywords in intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            scores[intent] = score

        # Get highest scoring intent
        primary_intent = (
            max(scores, key=lambda x: scores[x]) if any(scores.values()) else QueryIntent.UNKNOWN
        )

        # Extract potential code elements
        code_elements = []
        for word in query.split():
            if any(c in word for c in ["(", ")", ".", "_"]) and len(word) > 2:
                code_elements.append(word)

        return IntentAnalysis(
            primary_intent=primary_intent,
            code_elements=code_elements[:5],
            scope="repository",
            context_depth="normal",
            confidence=0.5,  # Lower confidence for fallback
        )

    def _fallback_follow_ups(self, query: str, response_text: str) -> List[str]:
        """Generate simple follow-up questions for fallback"""
        # Extract key terms from query
        words = re.findall(r'\b\w+\b', query.lower())
        key_terms = [word for word in words if len(word) > 4]

        follow_ups = []
        if key_terms:
            follow_ups.append(f"Can you show me more examples of {key_terms[0]}?")
            follow_ups.append(f"What are the best practices for {key_terms[0]}?")

        follow_ups.append("Can you explain this in more detail?")

        return follow_ups[:3]

    def _should_analyze_patterns(self, intent: IntentAnalysis) -> bool:
        """Determine if pattern analysis would be beneficial"""
        # Pattern analysis is useful for certain intents
        pattern_relevant_intents = {
            QueryIntent.ANALYZE,
            QueryIntent.REFACTOR,
            QueryIntent.COMPARE,
            QueryIntent.DEBUG,
        }
        return intent.primary_intent in pattern_relevant_intents

    async def _analyze_patterns(
        self,
        code_chunks: List[CodeChunk],
        intent: IntentAnalysis,
        context: QueryContext,
    ) -> Optional[Dict[str, Any]]:
        """Analyze patterns in code chunks"""
        try:
            # Convert chunks to pattern mining format
            chunks_data = [
                {
                    "file_path": chunk.file_path,
                    "content": chunk.content,
                    "language": chunk.language,
                    "start_line": chunk.start_line,
                    "end_line": chunk.end_line,
                    "metadata": chunk.metadata or {},
                }
                for chunk in code_chunks
            ]

            # Detect patterns
            pattern_result = await self.pattern_mining_client.detect_patterns(
                code_chunks=chunks_data,
                context={
                    "intent": intent.primary_intent.value,
                    "repository_id": context.repository_id,
                },
            )

            patterns = pattern_result.get("patterns", [])

            if not patterns:
                return None

            # Get recommendations if intent suggests improvement
            recommendations = None
            if intent.primary_intent in [QueryIntent.REFACTOR, QueryIntent.ANALYZE]:
                recommendations = await self.pattern_mining_client.get_recommendations(
                    code_context={
                        "patterns": patterns,
                        "code_elements": intent.code_elements,
                    },
                    intent=intent.primary_intent.value,
                    limit=3,
                )

            # Classify patterns if found
            classified_patterns = None
            if patterns:
                classification_result = (
                    await self.pattern_mining_client.classify_patterns(
                        patterns=patterns, classification_type="design_pattern"
                    )
                )
                classified_patterns = classification_result.get(
                    "classified_patterns", []
                )

            return {
                "patterns_detected": len(patterns),
                "pattern_types": (
                    [p.get("type") for p in classified_patterns]
                    if classified_patterns
                    else []
                ),
                "recommendations": recommendations,
                "quality_score": pattern_result.get("quality_score"),
                "anti_patterns_found": pattern_result.get("anti_patterns", []),
            }

        except CircuitBreakerError:
            logger.warning("pattern_analysis_circuit_breaker_open")
            return None
        except Exception as e:
            logger.error(
                "pattern_analysis_failed", error=str(e), chunk_count=len(code_chunks)
            )
            return None


def get_query_processor() -> QueryProcessor:
    """Factory function to get query processor instance"""
    return QueryProcessor()
